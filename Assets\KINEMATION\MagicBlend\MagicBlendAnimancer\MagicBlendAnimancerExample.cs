// Example usage of MagicBlend + Animancer integration
// Demonstrates various ways to play MagicBlendAssets through Animancer
// -----------------------------------------------------------------------------

using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;

namespace MagicBlendAnimancerIntegration.Examples
{
    /// <summary>
    /// Example component showing different ways to use MagicBlend with Animancer.
    /// </summary>
    public class MagicBlendAnimancerExample : MonoBehaviour
    {
        [Header("Animancer Setup")]
        [SerializeField] private HybridAnimancerComponent animancer;
        
        [Header("MagicBlend Assets")]
        [SerializeField] private MagicBlendAsset idleBlend;
        [SerializeField] private MagicBlendAsset walkBlend;
        [SerializeField] private MagicBlendAsset runBlend;
        [SerializeField] private MagicBlendAsset jumpBlend;
        
        [Header("MagicBlend Transitions (Alternative)")]
        [SerializeField] private MagicBlendTransition idleTransition;
        [SerializeField] private MagicBlendTransition attackTransition;
        
        [Header("Settings")]
        [SerializeField] private float defaultFadeTime = 0.25f;
        [SerializeField] private KeyCode testKey = KeyCode.Space;

        private void Start()
        {
            // Ensure MagicBlending is properly initialized
            var magicBlending = GetComponent<MagicBlending>();
            if (magicBlending != null && animancer != null)
            {
                magicBlending.InitializeForAnimancer(animancer);
                Debug.Log("[MagicBlendExample] MagicBlending initialized for Animancer");
            }
        }

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // Example 1: Direct MagicBlendAsset playback using extension methods
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                PlayIdleBlend();
            }
            
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                PlayWalkBlend();
            }
            
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                PlayRunBlend();
            }
            
            if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                PlayJumpBlend();
            }

            // Example 2: Using MagicBlendTransition assets
            if (Input.GetKeyDown(KeyCode.Alpha5))
            {
                PlayIdleTransition();
            }
            
            if (Input.GetKeyDown(KeyCode.Alpha6))
            {
                PlayAttackTransition();
            }

            // Example 3: Layered playback
            if (Input.GetKeyDown(KeyCode.Alpha7))
            {
                PlayUpperBodyBlend();
            }

            // Example 4: Stop current blend
            if (Input.GetKeyDown(KeyCode.Alpha0))
            {
                StopCurrentBlend();
            }
        }

        #region Example Methods

        /// <summary>
        /// Example 1: Direct MagicBlendAsset playback using extension methods.
        /// This is the simplest way to play a MagicBlendAsset.
        /// </summary>
        private void PlayIdleBlend()
        {
            if (idleBlend == null) return;
            
            // Direct playback - creates temporary transition internally
            var state = animancer.Play(idleBlend, defaultFadeTime);
            Debug.Log($"[MagicBlendExample] Playing idle blend: {state}");
        }

        private void PlayWalkBlend()
        {
            if (walkBlend == null) return;
            
            // You can also specify fade mode
            var state = animancer.Play(walkBlend, defaultFadeTime, FadeMode.FromStart);
            Debug.Log($"[MagicBlendExample] Playing walk blend: {state}");
        }

        private void PlayRunBlend()
        {
            if (runBlend == null) return;
            
            // TryPlay will reuse existing state if available
            var state = animancer.TryPlay(runBlend, defaultFadeTime);
            Debug.Log($"[MagicBlendExample] Playing run blend: {state}");
        }

        private void PlayJumpBlend()
        {
            if (jumpBlend == null) return;
            
            // GetOrCreate allows you to get the state without playing it
            var state = animancer.GetOrCreate(jumpBlend);
            if (state != null)
            {
                // Then play it manually with custom settings
                animancer.Play(state, 0.1f, FadeMode.FromStart);
                Debug.Log($"[MagicBlendExample] Playing jump blend: {state}");
            }
        }

        /// <summary>
        /// Example 2: Using pre-created MagicBlendTransition assets.
        /// This approach allows you to configure settings in the inspector.
        /// </summary>
        private void PlayIdleTransition()
        {
            if (idleTransition == null) return;
            
            // Play transition asset directly
            var state = animancer.Play(idleTransition);
            Debug.Log($"[MagicBlendExample] Playing idle transition: {state}");
        }

        private void PlayAttackTransition()
        {
            if (attackTransition == null) return;
            
            // Override the transition's default fade duration
            var state = animancer.Play(attackTransition, 0.1f);
            Debug.Log($"[MagicBlendExample] Playing attack transition: {state}");
        }

        /// <summary>
        /// Example 3: Playing on specific layers for layered animation.
        /// </summary>
        private void PlayUpperBodyBlend()
        {
            if (attackTransition == null) return;
            
            // Play on upper body layer (layer 1)
            if (animancer.Layers.Count > 1)
            {
                var state = animancer.Layers[1].Play(attackTransition, 0.15f);
                Debug.Log($"[MagicBlendExample] Playing upper body blend on layer 1: {state}");
            }
        }

        /// <summary>
        /// Example 4: Stopping blends.
        /// </summary>
        private void StopCurrentBlend()
        {
            // Stop specific asset
            if (idleBlend != null)
            {
                var stoppedState = animancer.Stop(idleBlend);
                if (stoppedState != null)
                {
                    Debug.Log($"[MagicBlendExample] Stopped idle blend: {stoppedState}");
                }
            }
            
            // Or stop all animations on base layer
            // animancer.Layers[0].Stop();
        }

        #endregion

        #region GUI for Testing

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("MagicBlend + Animancer Integration Test", GUI.skin.box);
            
            GUILayout.Space(10);
            GUILayout.Label("Direct Asset Playback:");
            if (GUILayout.Button("1 - Play Idle Blend")) PlayIdleBlend();
            if (GUILayout.Button("2 - Play Walk Blend")) PlayWalkBlend();
            if (GUILayout.Button("3 - Play Run Blend")) PlayRunBlend();
            if (GUILayout.Button("4 - Play Jump Blend")) PlayJumpBlend();
            
            GUILayout.Space(10);
            GUILayout.Label("Transition Asset Playback:");
            if (GUILayout.Button("5 - Play Idle Transition")) PlayIdleTransition();
            if (GUILayout.Button("6 - Play Attack Transition")) PlayAttackTransition();
            
            GUILayout.Space(10);
            GUILayout.Label("Advanced:");
            if (GUILayout.Button("7 - Play Upper Body Blend")) PlayUpperBodyBlend();
            if (GUILayout.Button("0 - Stop Current Blend")) StopCurrentBlend();
            
            GUILayout.Space(10);
            GUILayout.Label($"Current State: {(animancer.States.Current?.ToString() ?? "None")}");
            
            GUILayout.EndArea();
        }

        #endregion
    }
}
