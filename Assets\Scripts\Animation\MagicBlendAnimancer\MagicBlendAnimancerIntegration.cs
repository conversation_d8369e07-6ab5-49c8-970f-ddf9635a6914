// Created by Cascade AI: integration between KINEMATION MagicBlend and Animancer
// Allows playing a MagicBlendAsset through <PERSON><PERSON><PERSON> with a single call:
//     animancer.Play(myMagicBlendTransition);
// where "myMagicBlendTransition" is a ScriptableObject you can create via
// Create > Magic Blend > Transition.
// -----------------------------------------------------------------------------

#if UNITY_EDITOR || UNITY_STANDALONE || UNITY_ANDROID || UNITY_IOS
using System.Collections.Generic;
using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;
using HybridAnimancerComponent = NewAnimancer.HybridAnimancerComponent;
using IUpdatable = NewAnimancer.IUpdatable;
using Strings = NewAnimancer.Strings;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// A custom <see cref="AnimancerState"/> which integrates MagicBlend functionality into Animancer.
    /// This state connects MagicBlending's output playable directly into Animancer's playable graph,
    /// allowing Animancer to handle fades and mixing while MagicBlend handles the bone-level blending.
    /// </summary>
    public sealed class MagicBlendState : AnimancerState
    {
        private readonly MagicBlendAsset _asset;
        private MagicBlending _blending;
        private WeightForwarder _forwarder;
        private bool _isInitialized;

        public MagicBlendState(MagicBlendAsset asset)
        {
            _asset = asset ?? throw new System.ArgumentNullException(nameof(asset));
        }

        /// <summary>Gets the MagicBlendAsset associated with this state.</summary>
        public MagicBlendAsset Asset => _asset;

        /// <summary>Gets the MagicBlending component managing this state.</summary>
        public MagicBlending Blending => _blending;

        /********************************************************************/
        #region AnimancerState Implementation
        /********************************************************************/

        protected override void CreatePlayable(out Playable playable)
        {
            if (_isInitialized)
            {
                playable = _blending.OutputPlayable;
                return;
            }

            // Ensure MagicBlending component exists and is initialized
            var animancerComponent = Graph.Component;
            if (animancerComponent == null)
            {
                throw new System.InvalidOperationException("AnimancerComponent is required for MagicBlendState");
            }

            _blending = animancerComponent.gameObject.GetComponent<MagicBlending>();
            if (_blending == null)
            {
                _blending = animancerComponent.gameObject.AddComponent<MagicBlending>();
                Debug.Log("[MagicBlendState] Added MagicBlending component to GameObject");
            }

            // Initialize MagicBlending for Animancer if needed
            if (!_blending.playableGraph.IsValid() && animancerComponent is HybridAnimancerComponent hybrid)
            {
                _blending.InitializeForAnimancer(hybrid);
                Debug.Log("[MagicBlendState] Initialized MagicBlending for Animancer");
            }

            // Apply the asset without internal blending (Animancer handles fades)
            _blending.UpdateMagicBlendAsset(_asset, false, 0f);

            // Use MagicBlending's output playable directly
            playable = _blending.OutputPlayable;

            if (!playable.IsValid())
            {
                Debug.LogError("[MagicBlendState] MagicBlending OutputPlayable is invalid");
                playable = AnimationMixerPlayable.Create(Graph.PlayableGraph, 0);
            }

            // Forward weight changes to MagicBlending
            _forwarder = new WeightForwarder(this, _blending);
            Graph.RequirePreUpdate(_forwarder);

            _isInitialized = true;
        }

        public override float Length => _asset?.blendTime ?? 0.15f;

        public override bool IsLooping => _asset?.isAnimation ?? false;

        public override Vector3 AverageVelocity => Vector3.zero;

        public override AnimationClip Clip => _asset?.basePose;

        public override AnimancerState Clone(CloneContext context) => new MagicBlendState(_asset);

        protected override void OnSetIsPlaying()
        {
            base.OnSetIsPlaying();

            // Ensure MagicBlending component processes correctly when state playing status changes
            if (_blending != null && _isInitialized)
            {
                // MagicBlending handles its own playback state through weight forwarding
                // No additional action needed here as weight forwarding handles activation
            }
        }

        protected void OnDestroy()
        {
            // Clean up the weight forwarder
            if (_forwarder != null && Graph != null)
            {
                Graph.CancelPreUpdate(_forwarder);
                _forwarder = null;
            }
        }

        #endregion

        /********************************************************************/
        #region WeightForwarder Helper
        /********************************************************************/

        private sealed class WeightForwarder : IUpdatable
        {
            private readonly MagicBlendState _state;
            private readonly MagicBlending _blend;

            public WeightForwarder(MagicBlendState state, MagicBlending blend)
            {
                _state = state ?? throw new System.ArgumentNullException(nameof(state));
                _blend = blend ?? throw new System.ArgumentNullException(nameof(blend));
            }

            public int UpdatableIndex { get; set; } = IUpdatable.List.NotInList;

            public void Update()
            {
                if (_blend != null && _state != null)
                {
                    // Forward Animancer's effective weight to MagicBlending
                    _blend.externalWeight = _state.EffectiveWeight;

                    // Ensure MagicBlending processes when weight is above threshold
                    if (_state.EffectiveWeight > 0.001f && !_state.IsPlaying)
                    {
                        // This helps ensure MagicBlending stays active when needed
                    }
                }
            }
        }
        #endregion
    }

    // -------------------------------------------------------------------------
    /// <summary>
    /// A ScriptableObject that wraps a <see cref="MagicBlendAsset"/> so it can be played via Animancer.
    /// You can create it from the Unity Create menu:  Create/Magic Blend/Transition
    /// </summary>
    [CreateAssetMenu(menuName = "Magic Blend/Transition", fileName = "MagicBlendTransition", order = Strings.AssetMenuOrder)]
    public sealed class MagicBlendTransition : ScriptableObject, ITransition<MagicBlendState>, IAnimationClipSource
    {
        [SerializeField, Tooltip("The MagicBlendAsset to play")]
        private MagicBlendAsset _asset;

        [SerializeField, Tooltip("Default fade duration in seconds")]
        private float _fadeDuration = 0.25f;

        [SerializeField, Tooltip("Whether to use MagicBlend's internal blending or let Animancer handle fading")]
        private bool _useMagicBlendInternalFading = false;

        /// <summary>Gets or sets the MagicBlendAsset to play.</summary>
        public MagicBlendAsset Asset
        {
            get => _asset;
            set => _asset = value;
        }

        /// <summary>Gets or sets whether to use MagicBlend's internal blending.</summary>
        public bool UseMagicBlendInternalFading
        {
            get => _useMagicBlendInternalFading;
            set => _useMagicBlendInternalFading = value;
        }

        // ITransition implementation
        public float MaximumDuration => _asset?.isAnimation == true ? float.PositiveInfinity : (_asset?.blendTime ?? 0.15f);
        public float FadeDuration => _fadeDuration;
        public bool IsValid => _asset != null;
        public object Key => _asset;
        public FadeMode FadeMode => FadeMode.FixedSpeed;

        // ITransition<MagicBlendState> implementation
        public MagicBlendState State { get; private set; }

        public MagicBlendState CreateState()
        {
            State = new MagicBlendState(_asset);
            return State;
        }

        AnimancerState ITransition.CreateState() => CreateState();

        public void Apply(AnimancerState state)
        {
            if (state is MagicBlendState magicState)
            {
                State = magicState;

                if (_useMagicBlendInternalFading)
                {
                    // If using MagicBlend's internal fading, update the asset with blending enabled
                    magicState.Blending?.UpdateMagicBlendAsset(_asset, true, _fadeDuration);
                }
            }
        }

        // Clone method for compatibility
        public MagicBlendTransition Clone()
        {
            var clone = ScriptableObject.CreateInstance<MagicBlendTransition>();
            clone._asset = _asset;
            clone._fadeDuration = _fadeDuration;
            clone._useMagicBlendInternalFading = _useMagicBlendInternalFading;
            return clone;
        }

        #region IAnimationClipSource
        public void GetAnimationClips(List<AnimationClip> clips)
        {
            if (_asset != null)
            {
                if (_asset.basePose != null) clips.Add(_asset.basePose);
                if (_asset.overlayPose != null) clips.Add(_asset.overlayPose);
            }
        }
        #endregion

#if UNITY_EDITOR
        // Enhanced inspector for MagicBlendTransition
        [UnityEditor.CustomEditor(typeof(MagicBlendTransition))]
        private class Editor : UnityEditor.Editor
        {
            public override void OnInspectorGUI()
            {
                serializedObject.Update();

                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_asset"));
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_fadeDuration"));
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_useMagicBlendInternalFading"));

                // Show helpful information
                var asset = serializedObject.FindProperty("_asset").objectReferenceValue as MagicBlendAsset;
                if (asset != null)
                {
                    UnityEditor.EditorGUILayout.Space();
                    UnityEditor.EditorGUILayout.LabelField("Asset Info", UnityEditor.EditorStyles.boldLabel);
                    UnityEditor.EditorGUILayout.LabelField($"Base Pose: {(asset.basePose ? asset.basePose.name : "None")}");
                    UnityEditor.EditorGUILayout.LabelField($"Overlay Pose: {(asset.overlayPose ? asset.overlayPose.name : "None")}");
                    UnityEditor.EditorGUILayout.LabelField($"Is Animation: {asset.isAnimation}");
                    UnityEditor.EditorGUILayout.LabelField($"Blend Time: {asset.blendTime:F2}s");
                }

                serializedObject.ApplyModifiedProperties();
            }
        }
#endif
    }
}
#endif
