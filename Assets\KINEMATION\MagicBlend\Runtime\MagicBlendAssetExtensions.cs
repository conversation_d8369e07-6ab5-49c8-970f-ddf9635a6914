// Runtime extensions for MagicBlendAsset
// Provides utility functions to modify MagicBlendAsset properties at runtime
// -----------------------------------------------------------------------------

using KINEMATION.MagicBlend.Runtime;
using UnityEngine;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// Extension methods for MagicBlendAsset to modify properties at runtime
    /// </summary>
    public static class MagicBlendAssetExtensions
    {
        /// <summary>
        /// Sets the base pose of the MagicBlendAsset at runtime
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newBasePose">The new base pose animation clip</param>
        /// <param name="updateActiveBlending">Whether to update any active MagicBlending components using this asset</param>
        public static void SetBasePose(this MagicBlendAsset asset, AnimationClip newBasePose, bool updateActiveBlending = true)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set base pose on null MagicBlendAsset");
                return;
            }

            if (newBasePose == null)
            {
                Debug.LogWarning("[MagicBlendAssetExtensions] Setting base pose to null");
            }

            // Store the old pose for logging
            var oldPose = asset.basePose;

            // Set the new base pose
            asset.basePose = newBasePose;

            Debug.Log($"[MagicBlendAssetExtensions] Changed base pose from '{(oldPose ? oldPose.name : "null")}' to '{(newBasePose ? newBasePose.name : "null")}'");

            // Update any active MagicBlending components if requested
            if (updateActiveBlending)
            {
                ForceUpdateBasePose(asset);
            }
        }

        /// <summary>
        /// Sets the overlay pose of the MagicBlendAsset at runtime
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newOverlayPose">The new overlay pose animation clip</param>
        /// <param name="updateActiveBlending">Whether to update any active MagicBlending components using this asset</param>
        public static void SetOverlayPose(this MagicBlendAsset asset, AnimationClip newOverlayPose, bool updateActiveBlending = true)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set overlay pose on null MagicBlendAsset");
                return;
            }

            var oldPose = asset.overlayPose;
            asset.overlayPose = newOverlayPose;
            
            Debug.Log($"[MagicBlendAssetExtensions] Changed overlay pose from '{(oldPose ? oldPose.name : "null")}' to '{(newOverlayPose ? newOverlayPose.name : "null")}'");

            if (updateActiveBlending)
            {
                ForceUpdateBasePose(asset);
            }
        }

        /// <summary>
        /// Sets both base and overlay poses at once
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newBasePose">The new base pose animation clip</param>
        /// <param name="newOverlayPose">The new overlay pose animation clip</param>
        /// <param name="updateActiveBlending">Whether to update any active MagicBlending components using this asset</param>
        public static void SetPoses(this MagicBlendAsset asset, AnimationClip newBasePose, AnimationClip newOverlayPose, bool updateActiveBlending = true)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set poses on null MagicBlendAsset");
                return;
            }

            var oldBase = asset.basePose;
            var oldOverlay = asset.overlayPose;

            asset.basePose = newBasePose;
            asset.overlayPose = newOverlayPose;

            Debug.Log($"[MagicBlendAssetExtensions] Changed poses - Base: '{(oldBase ? oldBase.name : "null")}' -> '{(newBasePose ? newBasePose.name : "null")}', Overlay: '{(oldOverlay ? oldOverlay.name : "null")}' -> '{(newOverlayPose ? newOverlayPose.name : "null")}'");

            if (updateActiveBlending)
            {
                ForceUpdateBasePose(asset);
            }
        }

        /// <summary>
        /// Sets the blend time of the MagicBlendAsset at runtime
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newBlendTime">The new blend time in seconds</param>
        /// <param name="updateActiveBlending">Whether to update any active MagicBlending components using this asset</param>
        public static void SetBlendTime(this MagicBlendAsset asset, float newBlendTime, bool updateActiveBlending = true)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set blend time on null MagicBlendAsset");
                return;
            }

            var oldTime = asset.blendTime;
            asset.blendTime = Mathf.Max(0f, newBlendTime);
            
            Debug.Log($"[MagicBlendAssetExtensions] Changed blend time from {oldTime:F2}s to {asset.blendTime:F2}s");

            if (updateActiveBlending)
            {
                UpdateActiveMagicBlending(asset);
            }
        }

        /// <summary>
        /// Sets the global weight of the MagicBlendAsset at runtime
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newWeight">The new global weight (0-1)</param>
        /// <param name="updateActiveBlending">Whether to update any active MagicBlending components using this asset</param>
        public static void SetGlobalWeight(this MagicBlendAsset asset, float newWeight, bool updateActiveBlending = true)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set global weight on null MagicBlendAsset");
                return;
            }

            var oldWeight = asset.globalWeight;
            asset.globalWeight = Mathf.Clamp01(newWeight);
            
            Debug.Log($"[MagicBlendAssetExtensions] Changed global weight from {oldWeight:F2} to {asset.globalWeight:F2}");

            if (updateActiveBlending)
            {
                UpdateActiveMagicBlending(asset);
            }
        }

        /// <summary>
        /// Updates all active MagicBlending components that are using this asset
        /// </summary>
        /// <param name="asset">The MagicBlendAsset that was modified</param>
        private static void UpdateActiveMagicBlending(MagicBlendAsset asset)
        {
            // Find all MagicBlending components in the scene
            var magicBlendingComponents = Object.FindObjectsOfType<MagicBlending>();

            int updatedCount = 0;
            foreach (var blending in magicBlendingComponents)
            {
                // Check if this component is using our asset or if it's the current blend asset
                if (blending != null && (blending.BlendAsset == asset || blending.BlendAsset == null))
                {
                    // Force update the blending with the modified asset
                    // Use blending = false to immediately apply the new base pose without transition
                    blending.UpdateMagicBlendAsset(asset, false, 0f);
                    updatedCount++;

                    Debug.Log($"[MagicBlendAssetExtensions] Force updated MagicBlending component with new base pose: {(asset.basePose ? asset.basePose.name : "null")}");
                }
            }

            if (updatedCount > 0)
            {
                Debug.Log($"[MagicBlendAssetExtensions] Updated {updatedCount} active MagicBlending component(s)");
            }
            else
            {
                Debug.LogWarning("[MagicBlendAssetExtensions] No active MagicBlending components found to update");
            }
        }

        /// <summary>
        /// Creates a runtime copy of the MagicBlendAsset that can be safely modified without affecting the original
        /// </summary>
        /// <param name="asset">The original MagicBlendAsset</param>
        /// <returns>A runtime copy of the asset</returns>
        public static MagicBlendAsset CreateRuntimeCopy(this MagicBlendAsset asset)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot create runtime copy of null MagicBlendAsset");
                return null;
            }

            // Create a new instance
            var copy = ScriptableObject.CreateInstance<MagicBlendAsset>();
            
            // Copy all properties
            copy.rigAsset = asset.rigAsset;
            copy.blendTime = asset.blendTime;
            copy.blendCurve = new AnimationCurve(asset.blendCurve.keys);
            copy.basePose = asset.basePose;
            copy.overlayPose = asset.overlayPose;
            copy.overlaySpeed = asset.overlaySpeed;
            copy.isAnimation = asset.isAnimation;
            copy.globalWeight = asset.globalWeight;
            
            // Copy override overlays
            copy.overrideOverlays = new System.Collections.Generic.List<OverrideOverlay>(asset.overrideOverlays);
            
            // Copy layered blends
            copy.layeredBlends = new System.Collections.Generic.List<LayeredBlend>(asset.layeredBlends);
            
            Debug.Log($"[MagicBlendAssetExtensions] Created runtime copy of '{asset.name}'");
            
            return copy;
        }

        /// <summary>
        /// Forces an immediate update of the base pose in all active MagicBlending components
        /// This ensures the new base pose is immediately applied without waiting for the next frame
        /// </summary>
        /// <param name="asset">The MagicBlendAsset with the updated base pose</param>
        private static void ForceUpdateBasePose(MagicBlendAsset asset)
        {
            // Find all MagicBlending components in the scene
            var magicBlendingComponents = Object.FindObjectsOfType<MagicBlending>();

            foreach (var blending in magicBlendingComponents)
            {
                if (blending != null)
                {
                    // Force immediate update without blending to apply the new base pose
                    blending.UpdateMagicBlendAsset(asset, false, 0f);

                    Debug.Log($"[MagicBlendAssetExtensions] Force updated base pose to: {(asset.basePose ? asset.basePose.name : "null")}");
                }
            }

            // Also try to update through LayeredAnimationManager if available
            var layeredManager = Object.FindObjectOfType<Module.Mono.Animancer.RealsticFemale.LayeredAnimationManager>();
            if (layeredManager != null)
            {
                // Trigger MagicBlend on base layer (layer 0) to ensure base pose is visible
                layeredManager.TriggerMagicBlendOnLayer(asset, 0, false, 0.1f);
                Debug.Log($"[MagicBlendAssetExtensions] Updated through LayeredAnimationManager on BASE LAYER");
            }
        }

        /// <summary>
        /// Alternative method to update base pose with immediate playback through Animancer
        /// Use this if the standard SetBasePose doesn't work properly
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="newBasePose">The new base pose animation clip</param>
        /// <param name="animancer">The Animancer component to replay through</param>
        /// <param name="fadeTime">Fade time for the replay</param>
        public static void SetBasePoseAndReplay(this MagicBlendAsset asset, AnimationClip newBasePose,
            NewAnimancer.AnimancerComponent animancer, float fadeTime = 0.1f)
        {
            if (asset == null || animancer == null)
            {
                Debug.LogError("[MagicBlendAssetExtensions] Cannot set base pose - null asset or animancer");
                return;
            }

            // Store old pose for logging
            var oldPose = asset.basePose;

            // Set the new base pose
            asset.basePose = newBasePose;

            Debug.Log($"[MagicBlendAssetExtensions] Changed base pose from '{(oldPose ? oldPose.name : "null")}' to '{(newBasePose ? newBasePose.name : "null")}' and replaying");

            // Force replay through Animancer to ensure the new base pose is used
            var state = animancer.Play(asset, fadeTime);
            Debug.Log($"[MagicBlendAssetExtensions] Replayed asset with new base pose: {state}");
        }
    }
}
