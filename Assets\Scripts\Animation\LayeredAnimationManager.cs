// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using NewAnimancer.Units; // Added for MagicBlending integration
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public sealed class LayeredAnimationManager : MonoBehaviour
    {
        /************************************************************************************************************************/

        [SerializeField] private HybridAnimancerComponent _Animancer;
        [SerializeField] private AvatarMask _ActionMask;
        [SerializeField, Seconds] float _ActionFadeDuration = AnimancerGraph.DefaultFadeDuration;

        private AnimancerLayer _BaseLayer;
        private AnimancerLayer _ActionLayer;
        private bool _CanPlayActionFullBody;

        private void Awake()
        {
            _BaseLayer = _Animancer.Layers[0];
            _ActionLayer = _Animancer.Layers[1];

            _ActionLayer.Mask = _ActionMask;

        }

        /************************************************************************************************************************/

        public void PlayBase(ITransition transition, bool canPlayActionFullBody)
        {
            _CanPlayActionFullBody = canPlayActionFullBody;

            if (_CanPlayActionFullBody && _ActionLayer.TargetWeight > 0)
            {
                PlayActionFullBody(_ActionFadeDuration);
            }
            else
            {
                _BaseLayer.Play(transition);
            }
        }

        /************************************************************************************************************************/

        public void PlayAction(ITransition transition)
        {
            _ActionLayer.Play(transition);

            if (_CanPlayActionFullBody)
                PlayActionFullBody(transition.FadeDuration);
        }

        /************************************************************************************************************************/

        private void PlayActionFullBody(float fadeDuration)
        {
            var actionState = _ActionLayer.CurrentState;
            var baseState = _BaseLayer.Play(actionState.Clip, fadeDuration);
            baseState.NormalizedTime = actionState.NormalizedTime;
        }

        /************************************************************************************************************************/

        public void TriggerMagicBlend(MagicBlendAsset asset, bool useBlending = false, float blendTime = -1f)
        {
            if (asset == null)
            {
                Debug.LogError("[LayeredAnimationManager] Invalid MagicBlendAsset provided.");
                return;
            }

            float fadeDuration = blendTime >= 0f ? blendTime : _ActionFadeDuration;

            // Use the simplified MagicBlend integration through extension methods
            Debug.Log($"[LayeredAnimationManager] Playing MagicBlend asset '{asset.name}' on BASE LAYER with fade duration {fadeDuration}s");
            var playedState = _BaseLayer.Play(asset, fadeDuration);

            Debug.Log($"[LayeredAnimationManager] MagicBlend played on Base Layer. State: {playedState}, Base Pose: {(asset.basePose ? asset.basePose.name : "null")}, Overlay: {(asset.overlayPose ? asset.overlayPose.name : "null")}");
        }

        /************************************************************************************************************************/

        /// <summary>
        /// Triggers MagicBlend on a specific layer (0 = Base Layer, 1 = Action Layer)
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="layerIndex">Layer index (0 for base, 1 for action)</param>
        /// <param name="useBlending">Whether to use MagicBlend's internal blending (ignored in new implementation)</param>
        /// <param name="blendTime">Blend time in seconds</param>
        public void TriggerMagicBlendOnLayer(MagicBlendAsset asset, int layerIndex = 0, bool useBlending = false, float blendTime = -1f)
        {
            if (asset == null)
            {
                Debug.LogError("[LayeredAnimationManager] Invalid MagicBlendAsset provided.");
                return;
            }

            if (layerIndex < 0 || layerIndex >= _Animancer.Layers.Count)
            {
                Debug.LogError($"[LayeredAnimationManager] Invalid layer index: {layerIndex}. Available layers: {_Animancer.Layers.Count}");
                return;
            }

            float fadeDuration = blendTime >= 0f ? blendTime : _ActionFadeDuration;
            var targetLayer = _Animancer.Layers[layerIndex];
            string layerName = layerIndex == 0 ? "BASE" : "ACTION";

            Debug.Log($"[LayeredAnimationManager] Playing MagicBlend asset '{asset.name}' on {layerName} LAYER (index {layerIndex}) with fade duration {fadeDuration}s");
            var playedState = targetLayer.Play(asset, fadeDuration);

            Debug.Log($"[LayeredAnimationManager] MagicBlend played on {layerName} Layer. State: {playedState}, Base Pose: {(asset.basePose ? asset.basePose.name : "null")}, Overlay: {(asset.overlayPose ? asset.overlayPose.name : "null")}");
        }

        /************************************************************************************************************************/

        public void FadeOutUpperBody()
        {
            _ActionLayer.StartFade(0, _ActionFadeDuration);
        }

    }
}