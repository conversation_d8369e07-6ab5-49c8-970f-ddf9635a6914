// Enhanced integration between KINEMATION MagicBlend and Animancer
// Provides extension methods for direct MagicBlendAsset playback
// -----------------------------------------------------------------------------

#if UNITY_EDITOR || UNITY_STANDALONE || UNITY_ANDROID || UNITY_IOS
using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// Extension methods to enable direct MagicBlendAsset playback through Animancer.
    /// Allows usage like: animancer.Play(magicBlendAsset, fadeTime);
    /// </summary>
    public static class AnimancerMagicBlendExtensions
    {
        /// <summary>
        /// Plays a MagicBlendAsset directly through Animancer.
        /// Creates a temporary MagicBlendTransition internally.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The created MagicBlendState</returns>
        public static MagicBlendState Play(this AnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null)
            {
                Debug.LogError("[AnimancerMagicBlendExtensions] Cannot play null MagicBlendAsset");
                return null;
            }

            // Create a temporary transition
            var transition = ScriptableObject.CreateInstance<MagicBlendTransition>();
            transition.Asset = asset;

            // Play through Animancer
            var state = animancer.Play(transition, fadeDuration, mode);

            // Clean up the temporary transition (it's no longer needed after state creation)
            if (Application.isPlaying)
            {
                UnityEngine.Object.Destroy(transition);
            }
            else
            {
                UnityEngine.Object.DestroyImmediate(transition);
            }

            return state as MagicBlendState;
        }

        /// <summary>
        /// Plays a MagicBlendAsset on a specific Animancer layer.
        /// Creates a temporary MagicBlendTransition internally.
        /// </summary>
        /// <param name="layer">The Animancer layer</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The created MagicBlendState</returns>
        public static MagicBlendState Play(this AnimancerLayer layer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null)
            {
                Debug.LogError("[AnimancerMagicBlendExtensions] Cannot play null MagicBlendAsset");
                return null;
            }

            // Create a temporary transition
            var transition = ScriptableObject.CreateInstance<MagicBlendTransition>();
            transition.Asset = asset;

            // Play through Animancer layer
            var state = layer.Play(transition, fadeDuration, mode);

            // Clean up the temporary transition
            if (Application.isPlaying)
            {
                UnityEngine.Object.Destroy(transition);
            }
            else
            {
                UnityEngine.Object.DestroyImmediate(transition);
            }

            return state as MagicBlendState;
        }

        /// <summary>
        /// Tries to play a MagicBlendAsset if it exists in the state dictionary.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The MagicBlendState if found and played, null otherwise</returns>
        public static MagicBlendState TryPlay(this AnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null) return null;

            // Try to get existing state
            if (animancer.States.TryGet(asset, out var existingState) && existingState is MagicBlendState magicState)
            {
                return animancer.Play(magicState, fadeDuration, mode) as MagicBlendState;
            }

            // If not found, create new one
            return Play(animancer, asset, fadeDuration, mode);
        }

        /// <summary>
        /// Gets or creates a MagicBlendState for the given asset without playing it.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset</param>
        /// <returns>The MagicBlendState</returns>
        public static MagicBlendState GetOrCreate(this AnimancerComponent animancer, MagicBlendAsset asset)
        {
            if (asset == null) return null;

            // Try to get existing state
            if (animancer.States.TryGet(asset, out var existingState) && existingState is MagicBlendState magicState)
            {
                return magicState;
            }

            // Create new state
            var newState = new MagicBlendState(asset);
            newState.SetGraph(animancer.Graph);
            newState.Key = asset;  // This will automatically register the state
            return newState;
        }

        /// <summary>
        /// Stops a MagicBlendAsset if it's currently playing.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to stop</param>
        /// <returns>The stopped state, or null if not found</returns>
        public static MagicBlendState Stop(this AnimancerComponent animancer, MagicBlendAsset asset)
        {
            if (asset == null) return null;

            if (animancer.States.TryGet(asset, out var state) && state is MagicBlendState magicState)
            {
                magicState.Stop();
                return magicState;
            }

            return null;
        }
    }
}
#endif
