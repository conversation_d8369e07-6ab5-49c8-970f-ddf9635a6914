// Simple test script to verify base pose changes work correctly
// -----------------------------------------------------------------------------

using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;

/// <summary>
/// Simple test script to verify that base pose changes work correctly
/// </summary>
public class BasePoseTestScript : MonoBehaviour
{
    [Header("Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    
    [Header("Test Assets")]
    [SerializeField] private MagicBlendAsset testAsset;
    
    [Header("Base Pose Options")]
    [SerializeField] private AnimationClip basePose1;
    [SerializeField] private AnimationClip basePose2;
    [SerializeField] private AnimationClip basePose3;
    
    private int currentPoseIndex = 0;
    private AnimationClip[] poses;

    private void Start()
    {
        if (animancer == null)
            animancer = GetComponent<HybridAnimancerComponent>();
            
        // Setup pose array
        poses = new AnimationClip[] { basePose1, basePose2, basePose3 };
        
        // Start with the first pose
        if (testAsset != null && poses[0] != null)
        {
            testAsset.SetBasePoseAndReplay(poses[0], animancer, 0.25f);
            Debug.Log($"[BasePoseTestScript] Started with base pose: {poses[0].name}");
        }
    }

    private void Update()
    {
        // Press T to test base pose changes
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestBasePoseChange();
        }
        
        // Press R to replay current asset
        if (Input.GetKeyDown(KeyCode.R))
        {
            ReplayCurrentAsset();
        }
    }

    public void TestBasePoseChange()
    {
        if (testAsset == null || poses == null || poses.Length == 0)
        {
            Debug.LogWarning("[BasePoseTestScript] No test asset or poses configured");
            return;
        }

        // Cycle to next pose
        currentPoseIndex = (currentPoseIndex + 1) % poses.Length;
        var newPose = poses[currentPoseIndex];

        if (newPose == null)
        {
            Debug.LogWarning($"[BasePoseTestScript] Pose at index {currentPoseIndex} is null");
            return;
        }

        // Method 1: Use SetBasePose with LayeredAnimationManager integration
        testAsset.SetBasePose(newPose, true);

        // Method 2: Alternative - direct LayeredAnimationManager call
        var layeredManager = FindObjectOfType<Module.Mono.Animancer.RealsticFemale.LayeredAnimationManager>();
        if (layeredManager != null)
        {
            layeredManager.TriggerMagicBlendOnLayer(testAsset, 0, false, 0.1f); // Play on base layer
        }

        Debug.Log($"[BasePoseTestScript] ✅ Changed base pose to: {newPose.name} (Index: {currentPoseIndex}) - Playing on BASE LAYER");
    }

    public void ReplayCurrentAsset()
    {
        if (testAsset != null && animancer != null)
        {
            var state = animancer.Play(testAsset, 0.25f);
            Debug.Log($"[BasePoseTestScript] Replayed current asset: {state}");
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 300, 300, 150));
        GUILayout.Label("Base Pose Test", GUI.skin.box);
        
        if (GUILayout.Button("T - Test Base Pose Change"))
            TestBasePoseChange();
            
        if (GUILayout.Button("R - Replay Current Asset"))
            ReplayCurrentAsset();
            
        GUILayout.Space(10);
        
        if (testAsset != null)
        {
            GUILayout.Label($"Current Base Pose: {(testAsset.basePose ? testAsset.basePose.name : "None")}");
            GUILayout.Label($"Current Overlay: {(testAsset.overlayPose ? testAsset.overlayPose.name : "None")}");
        }
        
        GUILayout.EndArea();
    }
}
