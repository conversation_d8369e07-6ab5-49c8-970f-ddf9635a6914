// Simple test script to verify the new SimpleMagicBlend integration works
// -----------------------------------------------------------------------------

using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;

/// <summary>
/// Simple test script to verify that the new SimpleMagicBlend integration works correctly
/// </summary>
public class SimpleMagicBlendTest : MonoBehaviour
{
    [Header("Components")]
    [SerializeField] private AnimancerComponent animancer;
    
    [Header("Test Assets")]
    [SerializeField] private MagicBlendAsset testAsset;
    
    [Header("Base Pose Options")]
    [SerializeField] private AnimationClip basePose1;
    [SerializeField] private AnimationClip basePose2;
    [SerializeField] private AnimationClip basePose3;
    
    private int currentPoseIndex = 0;
    private AnimationClip[] poses;

    private void Start()
    {
        if (animancer == null)
            animancer = GetComponent<AnimancerComponent>();
            
        // Setup pose array
        poses = new AnimationClip[] { basePose1, basePose2, basePose3 };
        
        // Start with the first pose
        if (testAsset != null && poses[0] != null)
        {
            testAsset.basePose = poses[0];
            var state = animancer.Play(testAsset, 0.25f);
            Debug.Log($"[SimpleMagicBlendTest] Started with base pose: {poses[0].name}, State: {state}");
        }
    }

    private void Update()
    {
        // Press T to test base pose changes
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestBasePoseChange();
        }
        
        // Press R to replay current asset
        if (Input.GetKeyDown(KeyCode.R))
        {
            ReplayCurrentAsset();
        }
    }

    public void TestBasePoseChange()
    {
        if (testAsset == null || poses == null || poses.Length == 0)
        {
            Debug.LogWarning("[SimpleMagicBlendTest] No test asset or poses configured");
            return;
        }

        // Cycle to next pose
        currentPoseIndex = (currentPoseIndex + 1) % poses.Length;
        var newPose = poses[currentPoseIndex];
        
        if (newPose == null)
        {
            Debug.LogWarning($"[SimpleMagicBlendTest] Pose at index {currentPoseIndex} is null");
            return;
        }

        // Direct approach: Set base pose and replay
        testAsset.basePose = newPose;
        var state = animancer.Play(testAsset, 0.25f);
        
        Debug.Log($"[SimpleMagicBlendTest] ✅ Changed base pose to: {newPose.name} (Index: {currentPoseIndex}) - State: {state}");
    }

    public void ReplayCurrentAsset()
    {
        if (testAsset != null && animancer != null)
        {
            var state = animancer.Play(testAsset, 0.25f);
            Debug.Log($"[SimpleMagicBlendTest] Replayed current asset: {state}");
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 300, 300, 150));
        GUILayout.Label("Simple MagicBlend Test", GUI.skin.box);
        
        if (GUILayout.Button("T - Test Base Pose Change"))
            TestBasePoseChange();
            
        if (GUILayout.Button("R - Replay Current Asset"))
            ReplayCurrentAsset();
            
        GUILayout.Space(10);
        
        if (testAsset != null)
        {
            GUILayout.Label($"Current Base Pose: {(testAsset.basePose ? testAsset.basePose.name : "None")}");
            GUILayout.Label($"Current Overlay: {(testAsset.overlayPose ? testAsset.overlayPose.name : "None")}");
        }
        
        GUILayout.EndArea();
    }
}
