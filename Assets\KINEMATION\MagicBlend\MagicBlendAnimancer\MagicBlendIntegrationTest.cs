using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;

/// <summary>
/// Simple test script to verify MagicBlend + Animancer integration is working
/// </summary>
public class MagicBlendIntegrationTest : MonoBehaviour
{
    [Header("Required Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    
    [Header("Test Assets")]
    [SerializeField] private MagicBlendAsset testAsset;
    [SerializeField] private MagicBlendTransition testTransition;
    
    [Header("Settings")]
    [SerializeField] private float fadeTime = 0.25f;

    private void Start()
    {
        if (animancer == null)
        {
            animancer = GetComponent<HybridAnimancerComponent>();
        }
        
        Debug.Log("[MagicBlendIntegrationTest] Integration test started");
    }

    private void Update()
    {
        // Test direct asset playback
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestDirectAssetPlayback();
        }
        
        // Test transition asset playback
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestTransitionAssetPlayback();
        }
    }

    private void TestDirectAssetPlayback()
    {
        if (testAsset == null)
        {
            Debug.LogWarning("[MagicBlendIntegrationTest] No test asset assigned!");
            return;
        }

        try
        {
            var state = animancer.Play(testAsset, fadeTime);
            Debug.Log($"[MagicBlendIntegrationTest] ✅ Direct asset playback successful: {state}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MagicBlendIntegrationTest] ❌ Direct asset playback failed: {e.Message}");
        }
    }

    private void TestTransitionAssetPlayback()
    {
        if (testTransition == null)
        {
            Debug.LogWarning("[MagicBlendIntegrationTest] No test transition assigned!");
            return;
        }

        try
        {
            var state = animancer.Play(testTransition, fadeTime);
            Debug.Log($"[MagicBlendIntegrationTest] ✅ Transition asset playback successful: {state}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MagicBlendIntegrationTest] ❌ Transition asset playback failed: {e.Message}");
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label("MagicBlend Integration Test", GUI.skin.box);
        
        if (GUILayout.Button("1 - Test Direct Asset"))
        {
            TestDirectAssetPlayback();
        }
        
        if (GUILayout.Button("2 - Test Transition Asset"))
        {
            TestTransitionAssetPlayback();
        }
        
        GUILayout.Space(10);
        GUILayout.Label($"Current State: {(animancer?.States.Current?.ToString() ?? "None")}");
        
        GUILayout.EndArea();
    }
}
