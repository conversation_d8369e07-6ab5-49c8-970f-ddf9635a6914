#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using MagicBlendAnimancerIntegration;

namespace MagicBlendAnimancerIntegration.Editor
{
    public static class MagicBlendTransitionCreator
    {
        [MenuItem("Assets/Create/Magic Blend/Transition", false, 100)]
        public static void CreateMagicBlendTransition()
        {
            // Create the asset
            var asset = ScriptableObject.CreateInstance<MagicBlendTransition>();
            
            // Get the selected folder path
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(path))
            {
                path = "Assets";
            }
            else if (System.IO.Path.HasExtension(path))
            {
                path = path.Replace(System.IO.Path.GetFileName(path), "");
            }
            
            // Create unique asset path
            string assetPath = AssetDatabase.GenerateUniqueAssetPath(path + "/MagicBlendTransition.asset");
            
            // Create and save the asset
            AssetDatabase.CreateAsset(asset, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // Select the created asset
            EditorUtility.FocusProjectWindow();
            Selection.activeObject = asset;
            
            Debug.Log($"[MagicBlendTransitionCreator] Created MagicBlendTransition at: {assetPath}");
        }
        
        [MenuItem("Assets/Create/Magic Blend/Transition", true)]
        public static bool ValidateCreateMagicBlendTransition()
        {
            return true; // Always allow creation
        }
    }
}
#endif
