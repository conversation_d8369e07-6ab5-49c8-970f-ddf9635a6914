using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.KAnimationCore.Runtime.Rig; // For KRigComponent
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations; // For AnimationStream, IAnimationJob etc.
using Unity.Collections; // For NativeArray
using System.Collections.Generic; // For Dictionary

namespace YourProject.Animation 
{
    public class MagicBlendTransition : ITransition
    {
        private readonly MagicBlendAsset _magicBlendAsset;
        private readonly float _fadeDuration;

        public MagicBlendAsset Asset => _magicBlendAsset;

        public MagicBlendTransition(MagicBlendAsset magicBlendAsset, float fadeDuration)
        {
            _magicBlendAsset = magicBlendAsset;
            _fadeDuration = fadeDuration;
        }

        public object Key => _magicBlendAsset;
        public float FadeDuration => _fadeDuration;
        public FadeMode FadeMode => FadeMode.FixedSpeed;

        public AnimancerState CreateState()
        {
            return new MagicBlendAnimancerState(_magicBlendAsset);
        }

        public void Apply(AnimancerState state)
        {
        }
    }

    public struct LayeredBlendParameters
    {
        public float baseWeight;
        public float additiveWeight;
        public float localWeight;
    }

    public struct MagicBlendJob : NewAnimancer.IAnimancerStateJob, IAnimationJob
    {
        public NativeArray<TransformStreamHandle> boneHandles;
        public NativeArray<LayeredBlendParameters> blendParams;

        // Store the length for the IAnimancerStateJob.Length property
        public float length;

        public void Setup(NewAnimancer.AnimancerNode node)
        {
        }

        // IAnimancerStateJob.Length property implementation
        public float Length => length;

        // IAnimancerStateJob.ProcessRootMotion implementation (with time parameter)
        public void ProcessRootMotion(AnimationStream stream, double time)
        {
            if (!stream.isValid) return;
            var inputStream = stream.GetInputStream(0); // Assuming root motion from base pose
            if (inputStream.isValid)
            {
                stream.velocity = inputStream.velocity;
                stream.angularVelocity = inputStream.angularVelocity;
            }
            else
            {
                stream.velocity = Vector3.zero;
                stream.angularVelocity = Vector3.zero;
            }
        }

        // IAnimationJob.ProcessRootMotion implementation (without time parameter)
        public void ProcessRootMotion(AnimationStream stream)
        {
            ProcessRootMotion(stream, 0.0);
        }

        // IAnimationJob.ProcessAnimation implementation (without time parameter)
        public void ProcessAnimation(AnimationStream stream)
        {
            ProcessAnimation(stream, 0.0);
        }

        // IAnimancerStateJob.ProcessAnimation implementation (with time parameter)
        public void ProcessAnimation(AnimationStream stream, double time)
        {
            if (!stream.isValid || !boneHandles.IsCreated || boneHandles.Length == 0 || !blendParams.IsCreated) return;

            var basePoseStream = stream.GetInputStream(0);
            var overlayPoseStream = stream.GetInputStream(1);

            bool baseIsValid = basePoseStream.isValid;
            bool overlayIsValid = overlayPoseStream.isValid;

            if (!baseIsValid && !overlayIsValid) return; // Nothing to process

            for (int i = 0; i < boneHandles.Length; i++)
            {
                var boneHandle = boneHandles[i];
                // Ensure bone handle is valid for this stream (was bound to this animator)
                if (!boneHandle.IsValid(stream)) continue;

                Vector3 finalPosition;
                Quaternion finalRotation;

                if (baseIsValid && !overlayIsValid) // Only base is valid
                {
                    finalPosition = boneHandle.GetLocalPosition(basePoseStream);
                    finalRotation = boneHandle.GetLocalRotation(basePoseStream);
                }
                else if (!baseIsValid && overlayIsValid) // Only overlay is valid
                {
                    finalPosition = boneHandle.GetLocalPosition(overlayPoseStream);
                    finalRotation = boneHandle.GetLocalRotation(overlayPoseStream);
                }
                else // Both are valid, perform blending
                {
                    var baseLocalPosition = boneHandle.GetLocalPosition(basePoseStream);
                    var baseLocalRotation = boneHandle.GetLocalRotation(basePoseStream);

                    var overlayLocalPosition = boneHandle.GetLocalPosition(overlayPoseStream);
                    var overlayLocalRotation = boneHandle.GetLocalRotation(overlayPoseStream);

                    var parameters = blendParams[i];

                    finalPosition = Vector3.Lerp(baseLocalPosition, overlayLocalPosition, parameters.baseWeight);
                    finalRotation = Quaternion.Slerp(baseLocalRotation, overlayLocalRotation, parameters.baseWeight);

                    if (parameters.additiveWeight > 0.001f)
                    {
                        finalPosition += overlayLocalPosition * parameters.additiveWeight;
                        finalRotation = finalRotation * Quaternion.Slerp(Quaternion.identity, overlayLocalRotation, parameters.additiveWeight);
                    }

                    if (parameters.localWeight > 0.001f)
                    {
                        finalRotation = Quaternion.Slerp(finalRotation, overlayLocalRotation, parameters.localWeight);
                    }
                }

                boneHandle.SetLocalTRS(stream, finalPosition, finalRotation, Vector3.one, true);
            }
        }
    }

    public class MagicBlendAnimancerState : AnimancerState
    {
        private readonly MagicBlendAsset _asset;
        private AnimationClipPlayable _basePosePlayable;
        private Playable _overlaySourcePlayable;
        

        private NativeArray<TransformStreamHandle> _boneHandles;
        private NativeArray<LayeredBlendParameters> _blendParams;
        private KRigComponent _kRigComponent; // Changed from KRig

        public MagicBlendAsset Asset => _asset;

        public MagicBlendAnimancerState(MagicBlendAsset asset)
        {
            _asset = asset;
            if (_asset == null)
            {
                Debug.LogError("MagicBlendAsset is null for MagicBlendAnimancerState.");
            }
        }

        protected override void CreatePlayable(out Playable playable)
        {
            if (_asset == null)
            {
                Debug.LogError("Cannot create playable, MagicBlendAsset is null.");
                playable = Playable.Null;
                return;
            }

            var graph = Graph.PlayableGraph;
            if (!graph.IsValid())
            {
                Debug.LogError("PlayableGraph is not valid, cannot create playable for MagicBlendAnimancerState.");
                playable = Playable.Null;
                return;
            }

            var animator = Graph.Component.Animator;
            if (animator == null)
            {
                Debug.LogError("Animator is null, cannot create playable for MagicBlendAnimancerState.");
                playable = Playable.Null;
                return;
            }

            _kRigComponent = animator.GetComponentInChildren<KRigComponent>();
            if (_kRigComponent == null)
            {
                Debug.LogError("KRigComponent not found on Animator's hierarchy. Required for bone mapping.", animator);
                playable = Playable.Null;
                return;
            }

            if (_asset.basePose != null)
            {
                _basePosePlayable = AnimationClipPlayable.Create(graph, _asset.basePose);
                _basePosePlayable.SetApplyFootIK(false);
            }
            else
            {
                Debug.LogWarning("MagicBlendAsset is missing a basePose. Creating empty clip playable.", _asset);
                // Create an empty clip playable instead of mixer
                _basePosePlayable = AnimationClipPlayable.Create(graph, null);
            }

            if (_asset.overlayPose != null)
            {
                if (_asset.overrideOverlays != null && _asset.overrideOverlays.Count > 0 && AllOverridesValid(_asset.overrideOverlays))
                {
                    var overlayMixer = AnimationLayerMixerPlayable.Create(graph, _asset.overrideOverlays.Count + 1);
                    var mainOverlayPlayable = AnimationClipPlayable.Create(graph, _asset.overlayPose);
                    mainOverlayPlayable.SetSpeed(_asset.isAnimation ? _asset.overlaySpeed : 0f);
                    mainOverlayPlayable.SetApplyFootIK(false);
                    graph.Connect(mainOverlayPlayable, 0, overlayMixer, 0); 
                    overlayMixer.SetInputWeight(0, 1f);

                    for (int i = 0; i < _asset.overrideOverlays.Count; i++)
                    {
                        var overrideEntry = _asset.overrideOverlays[i];
                        if (overrideEntry.overlay == null) continue;
                        var overridePlayable = AnimationClipPlayable.Create(graph, overrideEntry.overlay);
                        overridePlayable.SetSpeed(_asset.isAnimation ? _asset.overlaySpeed : 0f);
                        overridePlayable.SetApplyFootIK(false);
                        int inputIndex = i + 1;
                        graph.Connect(overridePlayable, 0, overlayMixer, inputIndex); 
                        overlayMixer.SetInputWeight(inputIndex, overrideEntry.weight);
                        if (overrideEntry.mask != null)
                            overlayMixer.SetLayerMaskFromAvatarMask((uint)inputIndex, overrideEntry.mask);
                    }
                    _overlaySourcePlayable = overlayMixer;
                }
                else
                {
                    var overlayClipPlayable = AnimationClipPlayable.Create(graph, _asset.overlayPose);
                    overlayClipPlayable.SetSpeed(_asset.isAnimation ? _asset.overlaySpeed : 0f);
                    overlayClipPlayable.SetApplyFootIK(false);
                    _overlaySourcePlayable = overlayClipPlayable;
                }
            }
            else
            {
                Debug.LogWarning("MagicBlendAsset is missing an overlayPose. Creating empty playable.", _asset);
                // Create a simple mixer instead of null clip
                _overlaySourcePlayable = AnimationMixerPlayable.Create(graph, 0);
            }

            var rigBones = _kRigComponent.GetRigTransforms();
            if (rigBones == null || rigBones.Length == 0)
            {
                Debug.LogError("KRigComponent has no bones defined.", _kRigComponent);
                playable = Playable.Null;
                return;
            }

            _boneHandles = new NativeArray<TransformStreamHandle>(rigBones.Length, Allocator.Persistent);
            _blendParams = new NativeArray<LayeredBlendParameters>(rigBones.Length, Allocator.Persistent);
            
            var boneHierarchy = new Dictionary<string, int>();
            for(int i = 0; i < rigBones.Length; ++i)
            {
                if (rigBones[i] == null)
                {
                     Debug.LogWarning($"KRigComponent contains a null bone transform at index {i}.", _kRigComponent);
                     _boneHandles[i] = default; 
                     continue;
                }
                _boneHandles[i] = animator.BindStreamTransform(rigBones[i]);
                boneHierarchy[rigBones[i].name] = i;
            }

            for (int i = 0; i < rigBones.Length; ++i)
            {
                _blendParams[i] = new LayeredBlendParameters { baseWeight = 1f, additiveWeight = 0f, localWeight = 0f };
            }

            if (_asset.layeredBlends != null)
            {
                foreach (var layeredBlend in _asset.layeredBlends)
                {
                    if (layeredBlend.layer == null || layeredBlend.layer.elementChain == null) continue;
                    foreach (var element in layeredBlend.layer.elementChain) 
                    {
                        if (string.IsNullOrEmpty(element.name)) continue; 
                        
                        if (boneHierarchy.TryGetValue(element.name, out int boneIndex))
                        {
                            _blendParams[boneIndex] = new LayeredBlendParameters
                            {
                                baseWeight = layeredBlend.baseWeight,
                                additiveWeight = layeredBlend.additiveWeight,
                                localWeight = layeredBlend.localWeight
                            };
                        }
                    }
                }
            }
            
            var job = new MagicBlendJob
            {
                boneHandles = _boneHandles,
                blendParams = _blendParams,
                length = _asset != null && _asset.basePose != null ? _asset.basePose.length : 0f
            };

            // Create the job playable with proper input count
            var jobPlayable = AnimationScriptPlayable.Create(graph, job, 2);
            playable = jobPlayable;

            if (!playable.IsValid())
            {
                Debug.LogError("Failed to create job playable for MagicBlendAnimancerState.");
                playable = AnimationMixerPlayable.Create(graph, 0);
                return;
            }

            if (_basePosePlayable.IsValid())
                graph.Connect(_basePosePlayable, 0, playable, 0);

            if (_overlaySourcePlayable.IsValid())
                graph.Connect(_overlaySourcePlayable, 0, playable, 1);
        }
        
        private bool AllOverridesValid(List<OverrideOverlay> overrides)
        {
            if (overrides == null) return false; // Check if the list itself is null
            foreach (var o in overrides)
            {
                if (o.overlay == null) return false;
            }
            return true;
        }

        public override float Length => _asset != null && _asset.basePose != null ? _asset.basePose.length : 0f;
        public override bool IsLooping => _asset != null && _asset.basePose != null && _asset.basePose.isLooping;
        public override Object MainObject => _asset;

        public override AnimancerState Clone(CloneContext context)
        {
            var clone = new MagicBlendAnimancerState(_asset);
            clone.CopyFrom(this, context);
            return clone;
        }
        
        public override void CopyFrom(AnimancerState copyFrom, CloneContext context)
        {
            base.CopyFrom(copyFrom, context);
        }

        public override void Destroy()
        {
            if (_boneHandles.IsCreated)
                _boneHandles.Dispose();
            if (_blendParams.IsCreated)
                _blendParams.Dispose();
            
            base.Destroy(); 
        }
        
        public override double RawTime
        {
            get => _basePosePlayable.IsValid() ? _basePosePlayable.GetTime() : 0.0;
            set
            {
                if (_basePosePlayable.IsValid())
                    _basePosePlayable.SetTime(value);
                
                if (_overlaySourcePlayable.IsValid() && _asset != null && _asset.isAnimation)
                {
                    // Check if _overlaySourcePlayable is mixer or clip
                    if (_overlaySourcePlayable.IsPlayableOfType<AnimationMixerPlayable>() || _overlaySourcePlayable.IsPlayableOfType<AnimationLayerMixerPlayable>())
                    {
                        var mixer = (AnimationMixerPlayable)_overlaySourcePlayable; // Cast to base AnimationMixerPlayable
                        for (int i = 0; i < mixer.GetInputCount(); i++)
                        {
                            var inputPlayable = mixer.GetInput(i);
                            if (inputPlayable.IsValid() && inputPlayable.IsPlayableOfType<AnimationClipPlayable>())
                            {
                                inputPlayable.SetTime(value * _asset.overlaySpeed);
                            }
                        }
                    }
                    else if (_overlaySourcePlayable.IsPlayableOfType<AnimationClipPlayable>())
                    {
                         _overlaySourcePlayable.SetTime(value * _asset.overlaySpeed);
                    }
                }
            }
        }
    }
}
